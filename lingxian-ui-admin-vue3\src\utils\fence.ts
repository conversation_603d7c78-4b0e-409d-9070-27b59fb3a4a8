/**
 * 围栏管理相关的工具函数
 * 提供围栏绘制、坐标转换等常用功能
 */

import FabricCanvas from '@/utils/FabricCanvas'
import { canvasToImageWithDPR, imageToCanvasWithDPR, type ScreenInfo } from '@/store/modules/canvas/drawing'
import { Iposition } from '@/api/master/floor/type'
import { ElMessage } from 'element-plus'
import { AreaNode } from '@/api/master/area'
import {
  Circle,
  Rect,
  Polygon,
  IText,
  Line,
  Group,
  Shadow,
} from 'fabric'


// 围栏颜色配置接口
export interface FenceColor {
  color: string
  stroke: string
  strokeWidth: number
}

// 围栏数据类型定义
export interface FenceDate {
  type: 'circle' | 'rectangle' | 'polygon'
  points: any
}

// 区域位置接口定义
export interface CirclePoints {
  x: number
  y: number
  radius: number
}

export interface RectanglePoints {
  x: number
  y: number
  width: number
  height: number
}


/**
 * 围栏配置常量
 */
export const FENCE_CONFIG = {
  BASE_FONT_SIZE: 15,
  BASE_TEXT_PADDING: 4,
  BASE_STROKE_WIDTH: 2,
  BASE_POINT_RADIUS: 4,
  BASE_HANDLE_RADIUS: 35,
  MIN_VISIBLE_DISTANCE: 2,
  MIN_DISPLAY_DISTANCE: 0.001
} as const


export type PolygonPoints = Array<{ x: number, y: number }>

export interface AreaPosition {
  type: 'circle' | 'rectangle' | 'polygon' | 'none'
  points: CirclePoints | RectanglePoints | PolygonPoints
}

/**
 * 检查比例尺和原点配置
 * @param floorData 楼层数据
 * @param imgInfoSize 图片尺寸信息
 * @returns boolean 配置是否有效
 */
export function checkScaleAndOrigin(floorData: any, imgInfoSize: Iposition): boolean {
  if (!floorData.position) {
    ElMessage.warning('请先返回厂区管理设置比例尺和原点坐标')
    return false
  }
  // 检查比例尺配置
  if (!imgInfoSize.scaleSize) {
    ElMessage.warning('请先设置比例尺')
    return false
  }
  // 检查原点配置
  if (!imgInfoSize.orginMx || !imgInfoSize.orginMy) {
    ElMessage.warning('请先设置原点坐标')
    return false
  }
  return true
}

/**
 * 获取屏幕信息对象
 * @param move 移动和缩放信息
 * @param container 容器元素引用
 * @returns ScreenInfo 屏幕信息对象
 */
export function getScreenInfo(move: any, container: any): ScreenInfo {
  // 如果state.move中的值不合理，使用容器尺寸
  if (!move.screenWidth || !move.screenHeight) {
    if (container) {
      console.log('使用容器尺寸替代state中的屏幕尺寸')
      move.screenWidth = container.clientWidth
      move.screenHeight = container.clientHeight
      move.devicePixelRatio = window.devicePixelRatio || 1
    }
  }
  
  return {
    screenWidth: move.screenWidth,
    screenHeight: move.screenHeight,
    devicePixelRatio: move.devicePixelRatio
  }
}

/**
 * 添加围栏到画布
 * @param type 围栏类型
 * @param date 围栏数据
 * @param map FabricCanvas实例
 * @param imgInfoSize 图片尺寸信息
 * @param screenInfo 屏幕信息
 * @returns 围栏对象
 */
export function addFence(
  type: 'circle' | 'rectangle' | 'polygon', 
  date: FenceDate, 
  map: FabricCanvas,
  imgInfoSize: Iposition,
  screenInfo: ScreenInfo
) {
  let fenceType: any = null

  // 创建围栏工厂实例
  const fenceFactory = new FenceFactory(map.canvas)

  try {
    switch (type) {
      case 'circle':
        // 确保实际半径为正值
        const safeRealRadius = Math.max(date.points.radius || 0.001, 0.001)
        
        // 从实际坐标转换为画布坐标
        const { canvasX, canvasY, radius } = imageToCanvasWithDPR(
          { x: date.points.x, y: date.points.y, radius: safeRealRadius },
          map,
          imgInfoSize,
          screenInfo
        )
        
        // 确保画布半径为正值
        const safeCanvasRadius = Math.max(radius, 1)

        // 使用围栏工厂创建圆形围栏
        fenceType = fenceFactory.addCircleFence({ x: canvasX, y: canvasY }, safeCanvasRadius)
        break

      case 'rectangle':
        // 确保实际尺寸为正值
        const safeRealWidth = Math.max(date.points.width || 0.001, 0.001)
        const safeRealHeight = Math.max(date.points.height || 0.001, 0.001)
        
        // 从实际坐标转换为画布坐标
        const { canvasX: x, canvasY: y, width, height } = imageToCanvasWithDPR(
          { x: date.points.x, y: date.points.y, width: safeRealWidth, height: safeRealHeight },
          map,
          imgInfoSize,
          screenInfo
        )

        // 确保画布尺寸为正值
        const safeCanvasWidth = Math.max(width, 1)
        const safeCanvasHeight = Math.max(height, 1)

        // 使用围栏工厂创建矩形围栏
        fenceType = fenceFactory.addRectangleFence({ x, y }, safeCanvasWidth, safeCanvasHeight)
        break

      case 'polygon':
        // 转换多边形点坐标
        const polygonPoints = date.points as Array<{ x: number; y: number }>
        const canvasPoints = polygonPoints.map(point => {
          const result = imageToCanvasWithDPR(
            { x: point.x, y: point.y },
            map,
            imgInfoSize,
            screenInfo
          )
          return { x: result.canvasX, y: result.canvasY }
        })

        // 使用RealtimePolygonDrawer创建多边形围栏
        const polygonDrawer = new RealtimePolygonDrawer(map.canvas, fenceFactory)
        fenceType = polygonDrawer.renderPolygon(canvasPoints)
        break
    }
    
    // 设置公共属性
    if (fenceType) {
      fenceType.set({
        id: 'fence',
        objectCaching: false,
        selectable: false,
        hasBorders: false,
        hasControls: false,
        perPixelTargetFind: true, // 保持边框宽度一致，不随缩放变化
        strokeUniform: true, // 保持边框宽度一致，不随缩放变化
        noScaleCache: false
      })
    }
    
    return fenceType
  } catch (error) {
    console.error('添加围栏失败:', error, '类型:', type, '数据:', date)
    return null
  }
}

/**
 * 设置围栏颜色和样式
 * @param data 区域数据
 * @param isHighlighted 是否高亮显示
 * @returns 围栏样式对象
 */
export function setFenceColor(data: AreaNode | any, isHighlighted: boolean = false) {
  // 兼容直接传色值
  let color = data.color
  // 兜底：如果不是合法色值，强制蓝色
  if (!/^#([0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(color)) color = '#1e90ff'
  // 透明度处理
  const transparentColor = color + (isHighlighted ? '66' : '33')
  return {
    stroke: color,
    fill: transparentColor,
    strokeWidth: isHighlighted ? 2.5 : 1.5,
  }
}

/**
 * 清理围栏相关元素
 * @param map FabricCanvas实例
 */
export function cleanupFenceElements(map: FabricCanvas) {
  // 确保画布已初始化
  if (!map || !map.canvas) {
    return
  }
  
  // 查找并移除所有围栏相关对象
  const objectsToRemove = map.canvas.getObjects().filter((item: any) => 
    item.id === 'fence' || 
    item.id === 'circle-center' || 
    item.id === 'radius-line' || 
    item.id === 'radius-text' || 
    item.id === 'width-text' || 
    item.id === 'height-text' || 
    item.id === 'segment-text' ||
    (item.type === 'i-text' && (
      item.text.includes('radius') || 
      item.text.match(/^\d+\.?\d*$/) // 匹配纯数字文本
    ))
  )
  
  objectsToRemove.forEach((obj: any) => {
    map.canvas.remove(obj)
  })
  
  // 清理实时尺寸文本 - 直接从画布中删除所有i-text类型
  const textObjectsToRemove = map.canvas.getObjects().filter((item: any) => 
    item.type === 'i-text'
  );
  
  textObjectsToRemove.forEach((obj: any) => {
    map.canvas.remove(obj)
  });
  
  // 刷新画布
  map.canvas.requestRenderAll()
}



/**
 * 从画布坐标转换为围栏数据格式
 * @param type 围栏类型
 * @param fenceObj 围栏对象
 * @param map FabricCanvas实例
 * @param imgInfoSize 图片尺寸信息
 * @param screenInfo 屏幕信息
 * @returns 围栏数据
 */
export function convertToFenceData(
  type: 'circle' | 'rectangle' | 'polygon',
  fenceObj: any,
  map: FabricCanvas,
  imgInfoSize: Iposition,
  screenInfo: ScreenInfo
): FenceDate {
  const fenceDate: FenceDate = {
    type: type,
    points: null
  }
  
  switch (type) {
      
    case 'rectangle':
      // 获取矩形的实际尺寸（考虑缩放）
      const safeCanvasWidth = Math.max(fenceObj.getScaledWidth(), 1)
      const safeCanvasHeight = Math.max(fenceObj.getScaledHeight(), 1)

      // 使用坐标转换函数
      const { x, y, width, height } = canvasToImageWithDPR(
        { 
          x: fenceObj.left, 
          y: fenceObj.top, 
          width: safeCanvasWidth, 
          height: safeCanvasHeight
        },
        map,
        imgInfoSize,
        screenInfo
      )

      fenceDate.points = {
        x,
        y,
        width,
        height
      }
      break
      
    case 'circle':
      // 获取圆形的实际半径（考虑缩放）
      const safeCanvasRadius = Math.max(fenceObj.getScaledWidth() / 2, 1)

      // 使用坐标转换函数
      const { x: left, y: top, radius } = canvasToImageWithDPR(
        { 
          x: fenceObj.left, 
          y: fenceObj.top, 
          radius: safeCanvasRadius
        },
        map,
        imgInfoSize,
        screenInfo
      )

      // 确保实际半径也为正值
      const safeRadius = Math.max(radius, 0.001) // 最小实际半径（米）

      fenceDate.points = {
        x: left,
        y: top,
        radius: safeRadius
      }
      break

    case 'polygon':
      // 多边形坐标转换
      const polygonPoints = fenceObj.points || []
      const realPoints = polygonPoints.map((point: { x: number; y: number }) => {
        const realCoord = canvasToImageWithDPR(
          { x: point.x, y: point.y },
          map,
          imgInfoSize,
          screenInfo
        )
        return { x: realCoord.x, y: realCoord.y }
      })

      fenceDate.points = realPoints
      break
  }
  
  return fenceDate
}

/**
 * 为文本元素添加通用的编辑事件处理
 * @param textObj 文本对象
 * @param canvas 画布对象
 * @param onEditCallback 编辑完成回调
 */
function addCommonTextEditEvents(textObj: IText, canvas: any, onEditCallback?: (newValue: number) => void) {

  // 保存原始值以便恢复
  const originalValue = (textObj as any).originalValue || parseFloat(textObj.text);
  
  // 监听文本变化
  canvas.on('text:changed', (opt: any) => {
    if (opt.target !== textObj) return;
    
    const newText = opt.target.text;
    // 验证输入是否为有效数字
    if (/^[0-9]*\.?[0-9]*$/.test(newText) || newText === '') {
      // 解析新的数值
      const newValue = parseFloat(newText);
      
      // 验证数值是否有效
      if (isNaN(newValue) || newValue <= 0) {
        // 无效数值，恢复原始值
        textObj.set({ text: originalValue.toFixed(2) });
        canvas.renderAll();
        return;
      }
      
      // 应用新值（四舍五入到两位小数）
      const formattedValue = newValue.toFixed(2);
      textObj.set({ text: formattedValue });
      
      // 调用回调函数更新围栏尺寸
      if (onEditCallback) {
        onEditCallback(newValue);
      }

    } else {
      // 恢复到上一个有效值
      setTimeout(() => {
        textObj.set({ text: 1 });
        canvas.renderAll();
      }, 10);
    }
  });
  
  
  // 添加双击事件进入编辑模式
  textObj.on('mousedblclick', () => {
    textObj.enterEditing();
    textObj.selectAll();
  });
  
}

/**
 * 可编辑围栏类
 * 支持通过点击文本编辑尺寸，围栏会实时更新
 * 
 * 注意：以下方法已弃用或冗余，保留是为了兼容性：
 * - updatePolygonSegment：逻辑已移至 updateFenceByDimension 方法
 */
export class EditableFence {
  private map: FabricCanvas
  private fenceFactory: FenceFactory
  private imgInfoSize: Iposition
  private screenInfo: ScreenInfo
  private fenceObject: any = null
  private dimensionTexts: IText[] = []
  private dimensionLines: Line[] = []
  private centerMarker: Group | null = null
  private type: 'circle' | 'rectangle' | 'polygon'
  private id: string
  private onUpdateCallback?: (fenceData: FenceDate) => void
  private originalCanvasSelection: boolean = true // 保存画布原始选择状态

  constructor(
    map: FabricCanvas,
    imgInfoSize: Iposition,
    screenInfo: ScreenInfo,
    type: 'circle' | 'rectangle' | 'polygon',
    id: string = 'editable-fence'
  ) {
    this.map = map
    this.fenceFactory = new FenceFactory(map.canvas)
    this.imgInfoSize = imgInfoSize
    this.screenInfo = screenInfo
    this.type = type
    this.id = id
    
    // 添加缩放监听器
    this.addZoomListener()
  }

  /**
   * 设置更新回调函数
   */
  setUpdateCallback(callback: (fenceData: FenceDate) => void) {
    this.onUpdateCallback = callback
  }

  /**
   * 添加缩放监听器
   */
  private addZoomListener() {
    this.map.canvas.on('after:render', () => {
      this.updateElementsForZoom()
    })
  }

  /**
   * 根据缩放比例更新所有元素的大小和位置
   */
  private updateElementsForZoom() {
    const currentZoom = this.map.canvas.getZoom();
    const zoomFactor = 1 / currentZoom;
    
    // 更新围栏线宽 - 确保在高缩放下边框仍然可见
    if (this.fenceObject) {
      // 计算适当的边框宽度，确保在高缩放下仍然可见
      const minStrokeWidth = 1.5 / currentZoom; // 最小线宽，确保高缩放时仍可见
      const strokeWidth = Math.max(2 * zoomFactor, minStrokeWidth);
      
      this.fenceObject.set({
        strokeWidth: strokeWidth,
        strokeUniform: true // 保持边框宽度一致，不随缩放变化
      });
      
      // 确保围栏坐标更新
      this.fenceObject.setCoords();
    }
    
    // 更新尺寸线的线宽和位置（仅圆形有尺寸线）
    this.dimensionLines.forEach((line) => {
      // 计算适当的线宽，确保在高缩放下仍然可见
      const minStrokeWidth = 0.5 / currentZoom; // 最小线宽
      const strokeWidth = Math.max(1 * zoomFactor, minStrokeWidth);
      
      line.set({
        strokeWidth: strokeWidth,
        strokeUniform: true // 保持线宽一致，不随缩放变化
      });
      
      // 确保线段坐标更新
      line.setCoords();
    });
    
    // 更新文本的字体大小、内边距和位置
    this.dimensionTexts.forEach((text) => {
      const visualFontSize = (text as any).visualFontSize || 12;
      const visualDistance = (text as any).visualDistance || 8;
      
      // 计算缩放后的合适字体大小，确保文本在高缩放级别下仍然可见
      const minFontSize = 12; // 最小字体大小
      const maxFontSize = 30; // 最大字体大小
      let scaledFontSize = visualFontSize * zoomFactor;
      
      // 限制字体大小在合理范围内
      if (scaledFontSize < minFontSize / currentZoom) {
        scaledFontSize = minFontSize / currentZoom;
      } else if (scaledFontSize > maxFontSize / currentZoom) {
        scaledFontSize = maxFontSize / currentZoom;
      }
      
      // 基础属性更新
      text.set({
        fontSize: scaledFontSize,
        padding: 4 / currentZoom, // 内边距也随缩放调整，确保在高缩放下不会过大
        strokeWidth: 0.5 / currentZoom, // 文本描边宽度也随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)', // 确保背景色的透明度合适
        selectable: true, // 确保文本可被选择
        editable: true,   // 确保文本可编辑
        evented: true,    // 确保可以接收事件
        hasControls: false, // 不显示控制点
        hasBorders: true,   // 显示边框
        borderColor: 'rgba(24, 144, 255, 0.5)', // 边框颜色
        lockMovementX: false, // 允许移动以便选择
        lockMovementY: false, // 允许移动以便选择
        hoverCursor: 'text', // 鼠标悬停显示文本光标
        editingBorderColor: '#409EFF', // 编辑状态下的边框颜色
        cursorColor: '#409EFF', // 编辑光标颜色
        cursorWidth: 2 // 编辑光标宽度
      });
      
      // 提高文本的显示层级，确保它总是在其他元素之上
      const objIndex = this.map.canvas.getObjects().indexOf(text);
      if (objIndex !== -1) {
        this.map.canvas.remove(text);
        this.map.canvas.add(text);
      }
      
      // 根据类型更新位置
      if (this.type === 'circle' && this.fenceObject) {
        // 圆形文本位置更新 - 使用当前围栏的实际位置和半径
        const centerX = this.fenceObject.left;
        const centerY = this.fenceObject.top;
        const radius = this.fenceObject.getScaledWidth() / 2; // 使用getScaledWidth获取实际半径
        
        // 计算适当的距离，确保文本不会太近或太远
        const displayDistance = Math.max(visualDistance * zoomFactor, 10 / currentZoom);
        
        text.set({
          left: centerX + radius + displayDistance,
          top: centerY
        });
      } else if (this.type === 'rectangle' && this.fenceObject) {
        // 矩形文本位置更新 - 使用当前围栏的实际位置和尺寸
        const dimensionType = (text as any).dimensionType;
        const rectX = this.fenceObject.left;
        const rectY = this.fenceObject.top;
        const rectWidth = this.fenceObject.getScaledWidth();
        const rectHeight = this.fenceObject.getScaledHeight();
        
        // 计算适当的距离，确保文本不会太近或太远
        const displayDistance = Math.max(visualDistance * zoomFactor, 10 / currentZoom);
        
        if (dimensionType === 'width') {
          text.set({
            left: rectX + rectWidth / 2,
            top: rectY - displayDistance
          });
        } else if (dimensionType === 'height') {
          text.set({
            left: rectX - displayDistance,
            top: rectY + rectHeight / 2
          });
        }
      } else if (this.type === 'polygon' && this.fenceObject) {
        // 多边形文本位置更新 - 使用当前围栏的实际点坐标
        const segmentIndex = (text as any).segmentIndex;
        const points = (this.fenceObject as any).points;
        
        if (points && segmentIndex !== undefined && segmentIndex < points.length) {
          const currentPoint = points[segmentIndex];
          const nextPoint = points[(segmentIndex + 1) % points.length];
          
          // 重新计算边的中点
          const midX = (currentPoint.x + nextPoint.x) / 2;
          const midY = (currentPoint.y + nextPoint.y) / 2;
          
          // 重新计算边的角度和法线方向
          const dx = nextPoint.x - currentPoint.x;
          const dy = nextPoint.y - currentPoint.y;
          const angle = Math.atan2(dy, dx) * 180 / Math.PI;
          const perpAngle = (angle + 90) * Math.PI / 180;
          
          // 计算适当的距离，确保文本不会太近或太远
          const displayDistance = Math.max(visualDistance * zoomFactor, 10 / currentZoom);
          
          // 计算新的偏移位置
          const offsetX = Math.cos(perpAngle) * displayDistance;
          const offsetY = Math.sin(perpAngle) * displayDistance;
          
          text.set({
            left: midX + offsetX,
            top: midY + offsetY
          });
        }
      }
      
      // 确保坐标更新
      text.setCoords();
    });
    
    // 更新圆心标记
    if (this.centerMarker) {
      // 调整线宽，确保在高缩放级别下仍然可见
      const minStrokeWidth = 0.5 / currentZoom;
      const strokeWidth = Math.max(1.5 * zoomFactor, minStrokeWidth);
      
      // 更新圆心十字标记的大小
      this.centerMarker.forEachObject((obj: any) => {
        if (obj.type === 'line') {
          obj.set({
            strokeWidth: strokeWidth,
            strokeUniform: true // 保持线宽一致，不随缩放变化
          });
        }
      });
      
      // 更新圆心标记位置
      if (this.fenceObject && this.type === 'circle') {
        this.centerMarker.set({
          left: this.fenceObject.left,
          top: this.fenceObject.top
        });
      }
      
      // 确保坐标更新
      this.centerMarker.setCoords();
    }
    
    // 强制重新渲染
    this.map.canvas.requestRenderAll();
  }

  /**
   * 创建圆形围栏
   */
  createCircleFence(center: { x: number; y: number }, radius: number) {
    this.type = 'circle'
    
    // 创建圆形围栏
    this.fenceObject = this.fenceFactory.addCircleFence(center, radius)
    this.fenceObject.set({
      id: this.id,
      selectable: true,
      evented: true,
      hasControls: true,
      hasBorders: true,
      lockRotation: true, // 锁定旋转
      lockUniScaling: true, // 锁定不均匀缩放，确保圆形保持圆形
      transparentCorners: false,
      cornerColor: '#409EFF',
      cornerSize: 8,
      cornerStyle: 'circle',
      borderColor: '#409EFF',
      borderScaleFactor: 1.5,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      lockScalingX: false, // 允许X方向缩放
      lockScalingY: false, // 允许Y方向缩放
      lockScalingFlip: true, // 防止翻转
      originX: 'center', // 设置原点为中心
      originY: 'center', // 设置原点为中心
      centeredScaling: true, // 从中心点缩放
      centeredRotation: true, // 从中心点旋转
    })

    // 添加半径线和文本
    this.addRadiusDimension(center, radius)
    
    // 添加圆形调整事件监听
    this.addCircleResizeEvents()
    
    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建矩形围栏
   */
  createRectangleFence(position: { x: number; y: number }, width: number, height: number) {
    this.type = 'rectangle'
    
    // 创建矩形围栏
    this.fenceObject = this.fenceFactory.addRectangleFence(position, width, height)
    this.fenceObject.set({
      id: this.id,
      selectable: true,
      evented: true,
      hasControls: true,
      hasBorders: true,
      lockRotation: true, // 锁定旋转
      lockUniScaling: false, // 允许不均匀缩放，可以单独调整宽高
      transparentCorners: false,
      cornerColor: '#409EFF',
      cornerSize: 8,
      cornerStyle: 'circle',
      borderColor: '#409EFF',
      borderScaleFactor: 1.5,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
    })

    // 添加尺寸标注
    this.addRectangleDimensions(position, width, height)
    
    // 添加矩形调整事件监听
    this.addRectangleResizeEvents()
    
    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]) {
    this.type = 'polygon'
    
    // 使用RealtimePolygonDrawer创建多边形围栏
    const polygonDrawer = new RealtimePolygonDrawer(this.map.canvas, this.fenceFactory)
    this.fenceObject = polygonDrawer.renderPolygon(points)
    
    if (this.fenceObject) {
      this.fenceObject.set({
        id: this.id,
        selectable: true,
        evented: true,
        hasControls: true,
        hasBorders: true,
        lockRotation: false, // 多边形允许旋转
        transparentCorners: false,
        cornerColor: '#409EFF',
        cornerSize: 8,
        cornerStyle: 'circle',
        borderColor: '#409EFF',
        borderScaleFactor: 1.5,
        strokeUniform: true, // 保持边框宽度一致，不随缩放变化
        noScaleCache: false,
        objectCaching: false
      })
    }

    
    this.map.canvas.renderAll()
    return this
  }


  /**
   * 添加半径尺寸标注
   */
  private addRadiusDimension(center: { x: number; y: number }, radius: number) {
    const currentZoom = this.map.canvas.getZoom();
    const zoomFactor = 1 / currentZoom;
    const fontSize = 15 * zoomFactor;

    // 创建半径线
    const radiusLine = new Line(
      [center.x, center.y, center.x + radius, center.y],
      {
        stroke: '#409EFF',
        strokeWidth: 1 / currentZoom,
        selectable: false,
        evented: false
      }
    );

    // 创建半径文本
    const radiusText = new IText(`${(radius * 2).toFixed(2)}`, {
      left: center.x + radius + 30 * zoomFactor,
      top: center.y,
      fontSize: fontSize,
      fontFamily: 'Microsoft YaHei, Arial, sans-serif',
      fontWeight: 'bold',
      fill: '#1890ff',
      stroke: '#ffffff',
      strokeWidth: 0.5,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
      padding: 4 * zoomFactor,
      originX: 'left',
      originY: 'center',
      selectable: true,
      editable: true,
      evented: true,
      hasControls: false,
      hasBorders: true,
      borderColor: 'rgba(24, 144, 255, 0.5)',
      hoverCursor: 'text',
      editingBorderColor: '#409EFF',
      cursorColor: '#409EFF',
      cursorWidth: 2,
      lockMovementX: false,
      lockMovementY: false,
      lockRotation: true,
      lockScalingX: true,
      lockScalingY: true,
      shadow: new Shadow({
        color: 'rgba(0,0,0,0.3)',
        blur: 4,
        offsetX: 1,
        offsetY: 2
      })
    });

    // 保存原始值用于回退
    (radiusText as any).originalValue = radius * 2;
    (radiusText as any).dimensionType = 'radius';

    // 添加文本编辑事件
    this.addTextEditEvents(radiusText, 'radius');

    this.dimensionLines.push(radiusLine);
    this.dimensionTexts.push(radiusText);
    this.map.canvas.add(radiusLine);
    this.map.canvas.add(radiusText);
  }

  /**
   * 添加矩形尺寸标注（仅文本，无测距线）
   */
  private addRectangleDimensions(position: { x: number; y: number }, width: number, height: number) {
    const currentZoom = this.map.canvas.getZoom()
    const zoomFactor = 1 / currentZoom
    // 计算缩放后的合适字体大小
    const minFontSize = 12; // 最小字体大小
    const maxFontSize = 30; // 最大字体大小
    let fontSize = 15; // 基础字体大小

    // 根据缩放级别调整字体大小
    fontSize = fontSize * currentZoom;

    if (fontSize < minFontSize) {
      fontSize = minFontSize;
    } else if (fontSize > maxFontSize) {
      fontSize = maxFontSize;
    }

    // 使用完整的坐标转换获取实际宽度
    const realWidth = canvasToImageWithDPR(
      { x: position.x, y: position.y, width: width },
      this.map,
      this.imgInfoSize,
      this.screenInfo
    ).width
    
    // 宽度标注（仅文本，无测距线）
    const widthText = new IText(`${realWidth.toFixed(2)}`, {
      left: position.x + width / 2,
      top: position.y - 15 * zoomFactor,
      fontSize: fontSize,
      scaleX: 1,
      scaleY: 1,
      fontFamily: 'Microsoft YaHei, Arial, sans-serif',
      fontWeight: 'bold',
      fill: '#1890ff',
      stroke: '#ffffff',
      strokeWidth: 0.5,
      backgroundColor: 'rgba(255, 255, 255, 0.95)', // 增加背景不透明度
      textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
      padding: 4 * currentZoom, // 使用与尺子相同的内边距
      originX: 'center',
      originY: 'bottom',
      selectable: true, // 允许选择
      editable: true,   // 允许编辑
      evented: true,    // 确保可以接收事件
      hasControls: false, // 禁用控制点
      hasBorders: true, // 显示边框便于识别
      borderColor: 'rgba(24, 144, 255, 0.5)',
      hoverCursor: 'text',
      editingBorderColor: '#409EFF', // 编辑状态下的边框颜色
      cursorColor: '#409EFF', // 编辑光标颜色
      cursorWidth: 2, // 编辑光标宽度
      lockMovementX: false, // 允许移动以便选择
      lockMovementY: false, // 允许移动以便选择
      lockRotation: true,   // 锁定旋转
      lockScalingX: true,   // 锁定缩放
      lockScalingY: true,
      shadow: new Shadow({
        color: 'rgba(0,0,0,0.3)',
        blur: 4,
        offsetX: 1,
        offsetY: 2
      })
    })
    
    // 保存原始值用于回退
    ;(widthText as any).originalValue = realWidth
    
    // 保存原始视觉参数
    ;(widthText as any).visualDistance = 15
    ;(widthText as any).visualPadding = 12
    ;(widthText as any).visualFontSize = 15
    ;(widthText as any).rectX = position.x
    ;(widthText as any).rectY = position.y
    ;(widthText as any).rectWidth = width
    ;(widthText as any).rectHeight = height
    ;(widthText as any).dimensionType = 'width'
    
    // 先移除再添加，确保在最上层
    if (this.map && this.map.canvas) {
      this.map.canvas.remove(widthText);
      this.map.canvas.add(widthText);
    }
    
    this.addTextEditEvents(widthText, 'width')
    this.dimensionTexts.push(widthText)
    this.map.canvas.add(widthText)
    
    // 使用完整的坐标转换获取实际高度
    const realHeight = canvasToImageWithDPR(
      { x: position.x, y: position.y, height: height },
      this.map,
      this.imgInfoSize,
      this.screenInfo
    ).height
    
    // 高度标注（仅文本，无测距线，水平显示）
    const heightText = new IText(`${realHeight.toFixed(2)}`, {
      left: position.x - 15 * zoomFactor,
      top: position.y + height / 2,
      fontSize: fontSize, // 使用尺子的字体大小
      scaleX: 1,
      scaleY: 1,
      fontFamily: 'Microsoft YaHei, Arial, sans-serif',
      fontWeight: 'bold',
      fill: '#1890ff', // 使用尺子的蓝色
      stroke: '#ffffff',
      strokeWidth: 0.5,
      backgroundColor: 'rgba(255, 255, 255, 0.95)', // 增加背景不透明度
      textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
      padding: 4 * zoomFactor, // 使用与尺子相同的内边距
      originX: 'right',
      originY: 'center',
      angle: 0,  // 改为水平显示
      selectable: true, // 允许选择
      editable: true,   // 允许编辑
      evented: true,    // 确保可以接收事件
      hasControls: false, // 禁用控制点
      hasBorders: true, // 显示边框便于识别
      borderColor: 'rgba(24, 144, 255, 0.5)',
      hoverCursor: 'text',
      editingBorderColor: '#409EFF', // 编辑状态下的边框颜色
      cursorColor: '#409EFF', // 编辑光标颜色
      cursorWidth: 2, // 编辑光标宽度
      lockMovementX: false, // 允许移动以便选择
      lockMovementY: false, // 允许移动以便选择
      lockRotation: true,   // 锁定旋转
      lockScalingX: true,   // 锁定缩放
      lockScalingY: true,
      shadow: new Shadow({
        color: 'rgba(0,0,0,0.3)',
        blur: 4,
        offsetX: 1,
        offsetY: 2
      })
    })
    
    // 保存原始值用于回退
    ;(heightText as any).originalValue = realHeight
    
    // 保存原始视觉参数
    ;(heightText as any).visualDistance = 15
    ;(heightText as any).visualPadding = 12
    ;(heightText as any).visualFontSize = 15
    ;(heightText as any).rectX = position.x
    ;(heightText as any).rectY = position.y
    ;(heightText as any).rectWidth = width
    ;(heightText as any).rectHeight = height
    ;(heightText as any).dimensionType = 'height'
    
    // 先移除再添加，确保在最上层
    if (this.map && this.map.canvas) {
      this.map.canvas.remove(heightText);
      this.map.canvas.add(heightText);
    }
    
    this.addTextEditEvents(heightText, 'height')
    this.dimensionTexts.push(heightText)
    this.map.canvas.add(heightText)
  }



  /**
   * 添加文本编辑事件
   */
  private addTextEditEvents(textObj: IText, dimensionType: string) {
    addCommonTextEditEvents(textObj, this.map.canvas, (newValue) => {
      console.log('文本内容变化:', newValue)
      this.updateFenceByDimension(dimensionType, newValue)
    })
  }

  /**
   * 根据尺寸更新围栏
   */
  private updateFenceByDimension(dimensionType: string, newValue: number) {
    if (!this.fenceObject) return
    
    // 使用完整的坐标转换而不是简单的比例计算
    switch (this.type) {
      case 'circle':
        if (dimensionType === 'radius') {
          // 获取当前圆形的实际坐标和尺寸
          const currentCircle = canvasToImageWithDPR(
            { 
              x: this.fenceObject.left, 
              y: this.fenceObject.top,
              radius: this.fenceObject.radius
            },
            this.map,
            this.imgInfoSize,
            this.screenInfo
          );
          
          // 更新半径
          currentCircle.radius = newValue / 2;
          
          // 将更新后的尺寸转换回画布坐标
          const newCanvasData = imageToCanvasWithDPR(
            currentCircle,
            this.map,
            this.imgInfoSize,
            this.screenInfo
          );
          
          this.fenceObject.set({
            radius: newCanvasData.radius,
            width: newCanvasData.radius * 2,
            height: newCanvasData.radius * 2
          });

          // 更新半径线和文本
          this.updateRadiusDimension(
            { x: this.fenceObject.left, y: this.fenceObject.top },
            newCanvasData.radius
          );
        }
        break
        
      case 'rectangle':
        if (dimensionType === 'width' || dimensionType === 'height') {
          // 获取当前矩形的实际坐标和尺寸
          const currentRect = canvasToImageWithDPR(
            { 
              x: this.fenceObject.left, 
              y: this.fenceObject.top,
              width: this.fenceObject.width,
              height: this.fenceObject.height
            },
            this.map,
            this.imgInfoSize,
            this.screenInfo
          )
          
          // 更新对应的尺寸
          if (dimensionType === 'width') {
            currentRect.width = newValue
          } else if (dimensionType === 'height') {
            currentRect.height = newValue
          }
          
          // 将更新后的尺寸转换回画布坐标
          const newCanvasData = imageToCanvasWithDPR(
            currentRect,
            this.map,
            this.imgInfoSize,
            this.screenInfo
          )
          
          this.fenceObject.set({
            width: newCanvasData.width,
            height: newCanvasData.height
          })
        }
        this.updateRectangleDimensions()
        break
        
      case 'polygon':
        if (dimensionType.startsWith('segment-')) {
          // 多边形边长修改比较复杂，直接显示提示消息并更新显示
          ElMessage.info('多边形边长修改功能正在开发中，请重新绘制多边形')
          this.updateDimensionDisplay()
        }
        break
    }
    
    this.fenceObject.setCoords()
    this.map.canvas.renderAll()
    
    // 触发更新回调
    if (this.onUpdateCallback) {
      const fenceData = this.getFenceData()
      this.onUpdateCallback(fenceData)
    }
  }


  /**
   * 更新矩形尺寸显示（仅文本，无测距线）
   */
  private updateRectangleDimensions() {
    if (!this.fenceObject) return
    
    // 获取矩形的实际位置和尺寸（考虑缩放因素）
    const position = { 
      x: this.fenceObject.left, 
      y: this.fenceObject.top 
    }
    const width = this.fenceObject.getScaledWidth() // 使用getScaledWidth获取实际宽度
    const height = this.fenceObject.getScaledHeight() // 使用getScaledHeight获取实际高度
    const currentZoom = this.map.canvas.getZoom()
    const zoomFactor = 1 / currentZoom
    
    const visualDistance = 15
    
    // 更新宽度文本（使用完整的坐标转换）
    if (this.dimensionTexts[0]) {
      // 使用完整的坐标转换获取实际宽度
      const realWidth = canvasToImageWithDPR(
        { x: position.x, y: position.y, width: width },
        this.map,
        this.imgInfoSize,
        this.screenInfo
      ).width
      
      // 更新保存的位置信息
      ;(this.dimensionTexts[0] as any).rectX = position.x
      ;(this.dimensionTexts[0] as any).rectY = position.y
      ;(this.dimensionTexts[0] as any).rectWidth = width
      ;(this.dimensionTexts[0] as any).rectHeight = height
      
      // 保存原始值用于回退
      ;(this.dimensionTexts[0] as any).originalValue = realWidth
      
      this.dimensionTexts[0].set({
        left: position.x + width / 2,
        top: position.y - visualDistance * zoomFactor,
        text: `${realWidth.toFixed(2)}`
      })
      this.dimensionTexts[0].setCoords() // 确保坐标更新
    }
    
    // 更新高度文本（使用完整的坐标转换）
    if (this.dimensionTexts[1]) {
      // 使用完整的坐标转换获取实际高度
      const realHeight = canvasToImageWithDPR(
        { x: position.x, y: position.y, height: height },
        this.map,
        this.imgInfoSize,
        this.screenInfo
      ).height
      
      // 更新保存的位置信息
      ;(this.dimensionTexts[1] as any).rectX = position.x
      ;(this.dimensionTexts[1] as any).rectY = position.y
      ;(this.dimensionTexts[1] as any).rectWidth = width
      ;(this.dimensionTexts[1] as any).rectHeight = height
      
      // 保存原始值用于回退
      ;(this.dimensionTexts[1] as any).originalValue = realHeight
      
      this.dimensionTexts[1].set({
        left: position.x - visualDistance * zoomFactor,
        top: position.y + height / 2,
        text: `${realHeight.toFixed(2)}`
      })
      this.dimensionTexts[1].setCoords() // 确保坐标更新
    }
    
    // 强制重新渲染
    this.map.canvas.requestRenderAll()
  }

  /**
   * 更新尺寸显示
   */
  private updateDimensionDisplay() {
    switch (this.type) {
      case 'circle':
        if (this.fenceObject && this.dimensionTexts[0]) {
          // 使用完整的坐标转换获取实际半径（考虑缩放）
          const radius = this.fenceObject.getScaledWidth ? this.fenceObject.getScaledWidth() / 2 : this.fenceObject.radius;
          const realRadius = canvasToImageWithDPR(
            { x: this.fenceObject.left, y: this.fenceObject.top, radius: radius },
            this.map,
            this.imgInfoSize,
            this.screenInfo
          ).radius;
          
          // 更新文本内容
          this.dimensionTexts[0].set({ text: realRadius.toFixed(2) });
          
          // 确保文本位于顶层
          const textIndex = this.map.canvas.getObjects().indexOf(this.dimensionTexts[0]);
          if (textIndex !== -1) {
            this.map.canvas.remove(this.dimensionTexts[0]);
            this.map.canvas.add(this.dimensionTexts[0]);
          }
        }
        break;
        
      case 'rectangle':
        if (this.fenceObject) {
          // 获取矩形的实际尺寸（考虑缩放）
          const width = this.fenceObject.getScaledWidth ? this.fenceObject.getScaledWidth() : this.fenceObject.width;
          const height = this.fenceObject.getScaledHeight ? this.fenceObject.getScaledHeight() : this.fenceObject.height;
          
          if (this.dimensionTexts[0]) {
            // 使用完整的坐标转换获取实际宽度
            const realWidth = canvasToImageWithDPR(
              { x: this.fenceObject.left, y: this.fenceObject.top, width: width },
              this.map,
              this.imgInfoSize,
              this.screenInfo
            ).width;
            
            // 更新文本内容
            this.dimensionTexts[0].set({ text: `${realWidth.toFixed(2)}` });
            
            // 确保文本位于顶层
            const textIndex = this.map.canvas.getObjects().indexOf(this.dimensionTexts[0]);
            if (textIndex !== -1) {
              this.map.canvas.remove(this.dimensionTexts[0]);
              this.map.canvas.add(this.dimensionTexts[0]);
            }
          }
          
          if (this.dimensionTexts[1]) {
            // 使用完整的坐标转换获取实际高度
            const realHeight = canvasToImageWithDPR(
              { x: this.fenceObject.left, y: this.fenceObject.top, height: height },
              this.map,
              this.imgInfoSize,
              this.screenInfo
            ).height;
            
            // 更新文本内容
            this.dimensionTexts[1].set({ text: `${realHeight.toFixed(2)}` });
            
            // 确保文本位于顶层
            const textIndex = this.map.canvas.getObjects().indexOf(this.dimensionTexts[1]);
            if (textIndex !== -1) {
              this.map.canvas.remove(this.dimensionTexts[1]);
              this.map.canvas.add(this.dimensionTexts[1]);
            }
          }
        }
        break;
        
      case 'polygon':
        // 多边形需要重新计算每条边的长度
        if (this.fenceObject && this.fenceObject.points) {
          const points = this.fenceObject.points;
          
          for (let i = 0; i < points.length && i < this.dimensionTexts.length; i++) {
            const currentPoint = points[i];
            const nextPoint = points[(i + 1) % points.length];
            
            // 使用完整的坐标转换计算实际距离
            const point1Real = canvasToImageWithDPR(
              { x: currentPoint.x, y: currentPoint.y },
              this.map,
              this.imgInfoSize,
              this.screenInfo
            );
            const point2Real = canvasToImageWithDPR(
              { x: nextPoint.x, y: nextPoint.y },
              this.map,
              this.imgInfoSize,
              this.screenInfo
            );
            const realLength = Math.sqrt(
              Math.pow(point2Real.x - point1Real.x, 2) + 
              Math.pow(point2Real.y - point1Real.y, 2)
            );
            
            // 更新文本内容
            this.dimensionTexts[i].set({ text: realLength.toFixed(2) });
            
            // 确保文本位于顶层
            const textIndex = this.map.canvas.getObjects().indexOf(this.dimensionTexts[i]);
            if (textIndex !== -1) {
              this.map.canvas.remove(this.dimensionTexts[i]);
              this.map.canvas.add(this.dimensionTexts[i]);
            }
          }
        }
        break;
    }
    
    // 强制重新渲染
    this.map.canvas.requestRenderAll();
  }



  /**
   * 获取围栏数据
   */
  getFenceData(): FenceDate {
    if (!this.fenceObject) {
      return { type: this.type, points: null }
    }
    
    return convertToFenceData(this.type, this.fenceObject, this.map, this.imgInfoSize, this.screenInfo)
  }

  /**
   * 销毁围栏及其所有相关元素
   */
  destroy() {
    // 移除围栏对象
    if (this.fenceObject) {
      this.map.canvas.remove(this.fenceObject)
    }
    
    // 移除尺寸文本
    this.dimensionTexts.forEach(text => {
      this.map.canvas.remove(text)
    })
    
    // 移除尺寸线
    this.dimensionLines.forEach(line => {
      this.map.canvas.remove(line)
    })
    
    // 移除圆心标记
    if (this.centerMarker) {
      this.map.canvas.remove(this.centerMarker)
    }
    
    // 恢复画布拖动功能
    this.restoreCanvasDragging()
    
    // 清空数组
    this.dimensionTexts = []
    this.dimensionLines = []
    this.centerMarker = null
    this.fenceObject = null
    
    this.map.canvas.renderAll()
  }

  /**
   * 设置围栏样式
   */
  setStyle(style: { fill?: string; stroke?: string; strokeWidth?: number }) {
    if (this.fenceObject) {
      this.fenceObject.set(style)
      this.map.canvas.renderAll()
    }
  }

  /**
   * 获取围栏对象
   */
  getFenceObject() {
    return this.fenceObject
  }

  /**
   * 获取围栏工厂实例
   */
  getFenceFactory() {
    return this.fenceFactory
  }

  /**
   * 通用添加尺寸调整事件
   * @param updateMethod 更新方法函数
   */
  private addResizeEvents(updateMethod: () => void) {
    if (!this.fenceObject) return
    
    // 保存原始画布状态
    this.originalCanvasSelection = this.map.canvas.selection || false
    
    // 监听开始修改事件
    this.fenceObject.on('mousedown', () => {
      // 禁用画布拖动
      this.disableCanvasDragging()
    })
    
    // 监听对象修改过程中的事件（实时更新）
    this.fenceObject.on('scaling', () => {
      // 立即更新尺寸显示
      updateMethod()
      
      // 更新围栏数据并触发回调
      if (this.onUpdateCallback) {
        const fenceData = this.getFenceData()
        this.onUpdateCallback(fenceData)
      }
    })
    
    // 监听移动过程中的事件（实时更新）
    this.fenceObject.on('moving', () => {
      // 立即更新尺寸显示
      updateMethod()
      
      // 更新围栏数据并触发回调
      if (this.onUpdateCallback) {
        const fenceData = this.getFenceData()
        this.onUpdateCallback(fenceData)
      }
    })
    
    // 监听实时调整过程（增加对mouse:move的监听）
    this.map.canvas.on('mouse:move', () => {
      if (this.map.canvas.getActiveObject() === this.fenceObject) {
        // 当前对象正在被调整，实时更新尺寸显示
        updateMethod()
      }
    })
    
    // 修改完成时触发回调
    this.fenceObject.on('modified', () => {
      // 最终更新一次尺寸显示
      updateMethod()
      
      // 恢复画布拖动
      this.restoreCanvasDragging()
      
      // 确保更新围栏数据并触发回调
      if (this.onUpdateCallback) {
        const fenceData = this.getFenceData()
        this.onUpdateCallback(fenceData)
      }
    })
    
    // 监听鼠标释放事件（防止modified事件未触发的情况）
    this.map.canvas.on('mouse:up', () => {
      // 恢复画布拖动
      this.restoreCanvasDragging()
      
      // 如果当前围栏是活动对象，确保更新围栏数据
      if (this.map.canvas.getActiveObject() === this.fenceObject && this.onUpdateCallback) {
        const fenceData = this.getFenceData()
        this.onUpdateCallback(fenceData)
      }
    })
  }

  /**
   * 添加圆形调整事件监听
   */
  private addCircleResizeEvents() {
    if (!this.fenceObject) return;

    // 监听缩放事件
    this.fenceObject.on('scaling', (e: any) => {
      // 禁用画布拖动
      this.map.canvas.selection = false;
      this.map.canvas.isDragging = false;

      // 获取鼠标位置
      const pointer = this.map.canvas.getPointer(e.e);
      const center = {
        x: this.fenceObject.left,
        y: this.fenceObject.top
      };

      // 计算鼠标到圆心的距离
      const dx = pointer.x - center.x;
      const dy = pointer.y - center.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // 获取当前缩放比例
      const scaleX = this.fenceObject.getScaledWidth() / this.fenceObject.width;
      const scaleY = this.fenceObject.getScaledHeight() / this.fenceObject.height;
      
      // 根据鼠标位置判断是增大还是减小半径
      const currentRadius = this.fenceObject.radius;
      let newRadius;
      
      // 如果鼠标在圆外，增大半径；如果鼠标在圆内，减小半径
      if (distance > currentRadius) {
        // 增大半径，使用较大的缩放比例
        newRadius = Math.max(scaleX, scaleY) * currentRadius;
      } else {
        // 减小半径，使用较小的缩放比例
        newRadius = Math.min(scaleX, scaleY) * currentRadius;
      }

      // 确保半径不会小于最小值
      newRadius = Math.max(newRadius, 1);
      
      // 更新圆形属性
      this.fenceObject.set({
        radius: newRadius,
        scaleX: 1,
        scaleY: 1,
        width: newRadius * 2,
        height: newRadius * 2
      });

      // 更新半径线和文本
      if (this.dimensionTexts.length > 0) {
        this.updateRadiusDimension(center, newRadius);
      }

      this.map.canvas.renderAll();
    });

    // 监听移动事件
    this.fenceObject.on('moving', () => {
      // 禁用画布拖动
      this.map.canvas.selection = false;
      this.map.canvas.isDragging = false;

      if (this.dimensionTexts.length > 0) {
        const center = {
          x: this.fenceObject.left,
          y: this.fenceObject.top
        };
        const radius = this.fenceObject.radius;
        this.updateRadiusDimension(center, radius);
      }
    });

    // 监听移动结束事件
    this.fenceObject.on('modified', () => {
      // 恢复画布拖动
      this.map.canvas.selection = true;
      this.map.canvas.isDragging = true;
    });

    // 监听缩放结束事件
    this.fenceObject.on('scaling:end', () => {
      // 恢复画布拖动
      this.map.canvas.selection = true;
      this.map.canvas.isDragging = true;
    });
  }

  private updateRadiusDimension(center: { x: number; y: number }, radius: number) {
    // 更新半径线
    if (this.dimensionLines.length > 0) {
      const line = this.dimensionLines[0];
      line.set({
        x1: center.x,
        y1: center.y,
        x2: center.x + radius,
        y2: center.y
      });
    }

    // 更新半径文本
    if (this.dimensionTexts.length > 0) {
      const text = this.dimensionTexts[0];
      const currentZoom = this.map.canvas.getZoom();
      const visualDistance = 30; // 增加视觉距离
      const displayDistance = Math.max(visualDistance * currentZoom, 20);
      
      text.set({
        left: center.x + radius + displayDistance,
        top: center.y,
        text: `${(radius * 2).toFixed(2)}`,
        fontSize: 14,
        fontFamily: 'Arial',
        fill: '#409EFF',
        editable: true, // 允许编辑
        selectable: true, // 允许选择
        hasControls: true, // 显示控制点
        hasBorders: true, // 显示边框
        lockRotation: true, // 锁定旋转
        lockScalingX: false, // 允许水平缩放
        lockScalingY: false, // 允许垂直缩放
        lockUniScaling: true, // 锁定等比例缩放
        cornerColor: '#409EFF',
        cornerSize: 6,
        cornerStyle: 'circle',
        transparentCorners: false,
        borderColor: '#409EFF',
        borderScaleFactor: 1.5,
        padding: 5
      });

      // 添加文本编辑完成事件
      text.on('editing:exited', () => {
        const newValue = parseFloat(text.text);
        if (!isNaN(newValue) && newValue > 0) {
          // 更新圆形半径
          const newRadius = newValue / 2;
          this.fenceObject.set({
            radius: newRadius,
            width: newValue,
            height: newValue
          });
          // 更新半径线
          this.updateRadiusDimension(center, newRadius);
          this.map.canvas.renderAll();
        } else {
          // 如果输入无效，恢复原值
          text.set({
            text: `${(radius * 2).toFixed(2)}`
          });
          this.map.canvas.renderAll();
        }
      });
    }
  }

  /**
   * 添加矩形调整事件监听
   */
  private addRectangleResizeEvents() {
    this.addResizeEvents(() => this.updateRectangleDimensions())
  }



  /**
   * 禁用画布拖动
   * 在围栏被拖动/调整大小时调用，防止画布一起移动
   */
  private disableCanvasDragging() {
    if (!this.map.canvas) return
    
    // 保存当前状态以便恢复
    this.originalCanvasSelection = this.map.canvas.selection || false
    
    // 禁用画布拖动和选择
    this.map.canvas.selection = false
    this.map.canvas.isDragging = false
    
    // 将鼠标样式改为可用状态
    document.body.style.cursor = 'auto'
  }

  /**
   * 恢复画布拖动
   * 在围栏拖动/调整结束后调用，恢复画布原有状态
   */
  private restoreCanvasDragging() {
    if (!this.map.canvas) return
    
    // 恢复原始状态
    this.map.canvas.selection = this.originalCanvasSelection
    
    // 恢复默认鼠标样式
    document.body.style.cursor = 'default'
  }


}

/**
 * 围栏工厂类
 * 封装所有围栏创建和操作方法，从FabricCanvas中迁移而来
 */
export class FenceFactory {
  private canvas: any;
  private fenceColor: { color: string; stroke: string; strokeWidth: number };
  private polygonDrawing: boolean = false;
  private realtimeDimensionTexts: any[] = [];
  private polygonPoints: { x: number; y: number }[] = []; // 保存多边形点
  private polygonPreviewLine: any = null; // 多边形预览线
  private polygonPreviewShape: any = null; // 多边形预览形状

  constructor(canvas: any) {
    this.canvas = canvas;
    this.fenceColor = {
      color: 'rgba(30, 144, 255, 0.3)',
      stroke: '#1e90ff',
      strokeWidth: 2
    };
  }

  /**
   * 获取围栏颜色配置
   */
  getFenceColor() {
    return this.fenceColor;
  }

  /**
   * 添加圆形围栏
   */
  addCircleFence(center: { x: number; y: number }, radius: number) {
    const circle = new Circle({
      left: center.x,
      top: center.y,
      radius: radius,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: this.fenceColor.strokeWidth / this.canvas.getZoom(),
      originX: 'center',
      originY: 'center',
      lockUniScaling: true, // 锁定等比例缩放，确保圆形保持圆形
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      noScaleCache: false,
      objectCaching: false
    });
    
    this.canvas.add(circle);
    return circle;
  }

  /**
   * 添加矩形围栏
   */
  addRectangleFence(position: { x: number; y: number }, width: number, height: number) {
     // 创建最终的多边形
     const currentZoom = this.canvas.getZoom();
     const strokeWidth = this.fenceColor.strokeWidth / currentZoom;

    const rectangle = new Rect({
      left: position.x,
      top: position.y,
      width: width,
      height: height,
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: strokeWidth,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      noScaleCache: false,
      objectCaching: false
    });
    
    this.canvas.add(rectangle);
    return rectangle;
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]) {
    if (!points || points.length < 3) return null;
    
    const polygon = new Polygon(points, {
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: this.fenceColor.strokeWidth
    });
    
    this.canvas.add(polygon);
    return polygon;
  }
  
  /**
   * 添加多边形点
   * @param point 新的多边形点坐标
   */
  addPolygonPoint(point: { x: number; y: number }) {
    if (!this.polygonDrawing) {
      this.startPolygonDrawing();
    }
    
    this.polygonPoints.push(point);
    this.updatePolygonPreview();
    return true;
  }
  
  /**
   * 完成多边形绘制
   * @param lastPoint 最后一个点（可选）
   * @returns 创建的多边形对象，如果点数不足则返回null
   */
  finishPolygonDrawing(lastPoint?: { x: number; y: number }): any {
    // 如果提供了最后一个点，检查是否与第一个点接近
    if (lastPoint && this.polygonPoints.length > 0) {
      const firstPoint = this.polygonPoints[0];
      const distToFirst = Math.sqrt(
        Math.pow(lastPoint.x - firstPoint.x, 2) + 
        Math.pow(lastPoint.y - firstPoint.y, 2)
      );
      
      // 如果距离较远，添加最后一个点；否则自动闭合
      if (distToFirst > 10) {
        this.polygonPoints.push(lastPoint);
      }
    }
    
    // 确保至少有3个点
    if (this.polygonPoints.length < 3) {
      console.warn('多边形至少需要3个点');
      this.clearPolygonPreview();
      this.polygonDrawing = false;
      this.polygonPoints = [];
      return null;
    }
    
    // 创建最终的多边形
    const currentZoom = this.canvas.getZoom();
    const strokeWidth = 2 / currentZoom;
    
    const polygon = new Polygon(this.polygonPoints, {
      fill: this.fenceColor.color,
      stroke: this.fenceColor.stroke,
      strokeWidth: strokeWidth,
        selectable: false,
        evented: false,
        objectCaching: false
    });
    (polygon as any).id = 'fence';
    
    this.canvas.add(polygon);
    
    // 清理预览
    this.clearPolygonPreview();
    
    // 重置状态
    const resultPoints = [...this.polygonPoints];
    this.polygonDrawing = false;
    this.polygonPoints = [];
    
    console.log('多边形绘制完成，点数:', resultPoints.length);
    this.canvas.renderAll();
    
    return polygon;
  }

  /**
   * 设置圆形围栏
   */
  setCircleFence(circle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }, imgInfoSize?: any, screenInfo?: any) {
    if (!circle) return;
    
    // 计算半径
    const dx = point.x - startPoint.x;
    const dy = point.y - startPoint.y;
    const radius = Math.sqrt(dx * dx + dy * dy);
    
    // 确保在高缩放级别下边框仍然可见
    const currentZoom = this.canvas.getZoom();
    const strokeWidth = this.fenceColor.strokeWidth / currentZoom;
    
    // 更新圆形属性
    circle.set({
      radius: Math.max(radius, 1),
      lockUniScaling: true, // 锁定等比例缩放，确保圆形保持圆形
      strokeWidth: strokeWidth,
      strokeUniform: true // 保持边框宽度一致，不随缩放变化
    });
    
    // 更新实时尺寸显示
    this.updateRealtimeCircleDimension(circle, startPoint, imgInfoSize, screenInfo);
    
    this.canvas.renderAll();
  }

  /**
   * 设置矩形围栏
   */
  setRectangleFence(rectangle: any, point: { x: number; y: number }, startPoint: { x: number; y: number }, imgInfoSize?: any, screenInfo?: any) {
    if (!rectangle) return;
    
    // 计算宽高
    const width = Math.abs(point.x - startPoint.x);
    const height = Math.abs(point.y - startPoint.y);
    
    // 确定矩形左上角位置
    const left = Math.min(point.x, startPoint.x);
    const top = Math.min(point.y, startPoint.y);
    
    // 确保在高缩放级别下边框仍然可见
    const currentZoom = this.canvas.getZoom();
    const strokeWidth = this.fenceColor.strokeWidth / currentZoom;
    
    // 更新矩形属性
    rectangle.set({
      left: left,
      top: top,
      width: Math.max(width, 1),
      height: Math.max(height, 1),
      strokeWidth: strokeWidth,
      strokeUniform: true // 保持边框宽度一致，不随缩放变化
    });
    
    // 更新实时尺寸显示
    this.updateRealtimeRectangleDimension(rectangle, imgInfoSize, screenInfo);
    
    this.canvas.renderAll();
  }

  /**
   * 更新半径线
   */
  updateRadiusLine(radiusLine: any, center: { x: number; y: number }, endPoint: { x: number; y: number }) {
    if (!radiusLine) return;
    
    radiusLine.set({
      x1: center.x,
      y1: center.y,
      x2: endPoint.x,
      y2: endPoint.y
    });
    
    this.canvas.renderAll();
  }

  /**
   * 更新实时圆形尺寸标注
   */
  updateRealtimeCircleDimension(circle: any, center: { x: number; y: number }, imgInfoSize?: any, screenInfo?: any) {
    // 清除之前的实时标注
    this.clearRealtimeDimensionTexts();
    
    const currentZoom = this.canvas.getZoom();
    const zoomFactor = 1 / currentZoom;
    const strokeWidth = this.fenceColor.strokeWidth / currentZoom;
    let radiusText;
    
    // 获取实际半径（考虑缩放因素）
    const actualRadius = circle.getScaledWidth ? circle.getScaledWidth() / 2 : circle.radius;
    
    // 计算适当的显示距离，确保文本不会太近或太远
    const displayDistance = Math.max(15 * zoomFactor, 10 / currentZoom);
    
    // 计算缩放后的合适字体大小
    const minFontSize = 12; // 最小字体大小
    const maxFontSize = 30; // 最大字体大小
    let fontSize = 15 * zoomFactor;
    
    // 限制字体大小在合理范围内
    if (fontSize < minFontSize / currentZoom) {
      fontSize = minFontSize / currentZoom;
    } else if (fontSize > maxFontSize / currentZoom) {
      fontSize = maxFontSize / currentZoom;
    }
    
    // 创建新的半径标注
    if (imgInfoSize && screenInfo) {
      // 转换为实际距离
    const realRadius = canvasToImageWithDPR(
        { x: center.x, y: center.y, radius: actualRadius },
        { canvas: this.canvas },
      imgInfoSize,
      screenInfo
      ).radius;
    
      radiusText = new IText(`${realRadius.toFixed(2)}`, {
        left: center.x + actualRadius + displayDistance,
      top: center.y,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#f50', // 使用尺子的文本颜色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
      originX: 'left',
      originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    } else {
      // 直接使用画布距离
      radiusText = new IText(`${actualRadius.toFixed(2)}`, {
        left: center.x + actualRadius + displayDistance,
        top: center.y,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#f50', // 使用尺子的文本颜色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
        originX: 'left',
        originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    }
    
    this.realtimeDimensionTexts.push(radiusText);
    this.canvas.add(radiusText);
    
    // 确保文本在顶层
    if (radiusText) {
      this.canvas.remove(radiusText);
      this.canvas.add(radiusText);
    }
    
    this.canvas.renderAll();
  }

  /**
   * 更新实时矩形尺寸标注
   */
  updateRealtimeRectangleDimension(rectangle: any, imgInfoSize?: any, screenInfo?: any) {
    // 清除之前的实时标注
    this.clearRealtimeDimensionTexts();
    
    const currentZoom = this.canvas.getZoom();
    const zoomFactor = 1 / currentZoom;
    
    // 获取矩形实际尺寸（考虑缩放）
    const actualWidth = rectangle.getScaledWidth ? rectangle.getScaledWidth() : rectangle.width;
    const actualHeight = rectangle.getScaledHeight ? rectangle.getScaledHeight() : rectangle.height;
    
    // 计算适当的显示距离，确保文本不会太近或太远
    const displayDistance = Math.max(15 * zoomFactor, 10 / currentZoom);
    
    // 计算缩放后的合适字体大小
    const minFontSize = 12; // 最小字体大小
    const maxFontSize = 30; // 最大字体大小
    let fontSize = 15 * zoomFactor;
    
    // 限制字体大小在合理范围内
    if (fontSize < minFontSize / currentZoom) {
      fontSize = minFontSize / currentZoom;
    } else if (fontSize > maxFontSize / currentZoom) {
      fontSize = maxFontSize / currentZoom;
    }
    
    let widthText, heightText;
    
    // 创建新的宽度和高度标注
    if (imgInfoSize && screenInfo) {
      // 转换为实际距离
    const realDimensions = canvasToImageWithDPR(
        { 
          x: rectangle.left, 
          y: rectangle.top, 
          width: actualWidth, 
          height: actualHeight 
        },
        { canvas: this.canvas },
      imgInfoSize,
      screenInfo
      );
      
      // 宽度标注
      widthText = new IText(`${realDimensions.width.toFixed(2)}`, {
        left: rectangle.left + actualWidth / 2,
        top: rectangle.top - displayDistance,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
      originX: 'center',
      originY: 'bottom',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
      
      // 高度标注
      heightText = new IText(`${realDimensions.height.toFixed(2)}`, {
        left: rectangle.left - displayDistance,
        top: rectangle.top + actualHeight / 2,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
      originX: 'right',
      originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    } else {
      // 直接使用画布距离
      widthText = new IText(`${actualWidth.toFixed(2)}`, {
        left: rectangle.left + actualWidth / 2,
        top: rectangle.top - displayDistance,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
        originX: 'center',
        originY: 'bottom',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
      
      heightText = new IText(`${actualHeight.toFixed(2)}`, {
        left: rectangle.left - displayDistance,
        top: rectangle.top + actualHeight / 2,
        fontSize: fontSize,
        fontFamily: 'Microsoft YaHei, Arial, sans-serif',
        fontWeight: 'bold',
        fill: '#1890ff', // 使用尺子的蓝色
        stroke: '#ffffff',
        strokeWidth: 0.5 / currentZoom, // 描边宽度随缩放调整
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        textBackgroundColor: 'rgba(255, 255, 255, 0.95)',
        padding: 4 / currentZoom, // 内边距随缩放调整
        originX: 'right',
        originY: 'center',
        shadow: new Shadow({
          color: 'rgba(0,0,0,0.3)',
          blur: 4,
          offsetX: 1,
          offsetY: 2
        })
      });
    }
    
    this.realtimeDimensionTexts.push(widthText, heightText);
    
    // 确保文本在顶层 - 先添加，再删除重新添加
    this.canvas.add(widthText);
    this.canvas.add(heightText);
    
    // 确保文本在顶层
    if (widthText) {
      this.canvas.remove(widthText);
      this.canvas.add(widthText);
    }
    
    if (heightText) {
      this.canvas.remove(heightText);
      this.canvas.add(heightText);
    }
    
    this.canvas.renderAll();
  }

  /**
   * 清理实时尺寸文本
   */
  clearRealtimeDimensionTexts() {
    this.realtimeDimensionTexts.forEach(text => {
      if (text) {
        this.canvas.remove(text);
      }
    });
    this.realtimeDimensionTexts = [];
  }

  /**
   * 开始多边形绘制
   */
  startPolygonDrawing() {
    this.polygonDrawing = true;
    this.polygonPoints = [];
    this.clearPolygonPreview();
    console.log('开始多边形绘制');
  }

  /**
   * 是否正在绘制多边形
   */
  isPolygonDrawing() {
    return this.polygonDrawing;
  }

  /**
   * 取消多边形绘制
   */
  cancelPolygonDrawing() {
    this.polygonDrawing = false;
    this.polygonPoints = [];
    this.clearPolygonPreview();
    console.log('取消多边形绘制');
  }

  /**
   * 更新多边形橡皮筋效果
   * @param mousePoint 当前鼠标位置
   */
  updatePolygonRubberBand(mousePoint: { x: number; y: number }) {
    if (!this.polygonDrawing || this.polygonPoints.length === 0) return;
    
    // 清除之前的预览
    this.clearPolygonPreview();
    
    const currentZoom = this.canvas.getZoom();
    const strokeWidth = 2 / currentZoom;
    
    // 如果只有一个点，绘制从第一个点到鼠标的线
    if (this.polygonPoints.length === 1) {
      this.polygonPreviewLine = new Line(
        [this.polygonPoints[0].x, this.polygonPoints[0].y, mousePoint.x, mousePoint.y],
        {
          stroke: this.fenceColor.stroke,
          strokeWidth: strokeWidth,
          selectable: false,
          evented: false,
          objectCaching: false
        }
      );
      (this.polygonPreviewLine as any).id = 'polygon-preview-line';
      this.canvas.add(this.polygonPreviewLine);
    }
    // 如果有两个或更多点，绘制完整的预览多边形
    else if (this.polygonPoints.length >= 2) {
      // 创建包含鼠标位置的临时点数组
      const previewPoints = [...this.polygonPoints, mousePoint];
      
      // 创建预览多边形（使用更透明的填充）
      const previewFill = this.fenceColor.color.replace(/[\d\.]+\)$/g, '0.1)'); // 将透明度改为0.1
      
      this.polygonPreviewShape = new Polygon(previewPoints, {
        fill: previewFill,
        stroke: this.fenceColor.stroke,
        strokeWidth: strokeWidth,
      selectable: false,
        evented: false,
        objectCaching: false
      });
      (this.polygonPreviewShape as any).id = 'polygon-preview';
      
      this.canvas.add(this.polygonPreviewShape);
    }
    
    this.canvas.renderAll();
  }
  
  /**
   * 更新多边形预览
   */
  private updatePolygonPreview() {
    // 清除之前的预览
    this.clearPolygonPreview();
    
    // 如果有足够的点，创建临时多边形
    if (this.polygonPoints.length >= 3) {
      const currentZoom = this.canvas.getZoom();
      const strokeWidth = 2 / currentZoom;
      
      this.polygonPreviewShape = new Polygon(this.polygonPoints, {
        fill: this.fenceColor.color,
        stroke: this.fenceColor.stroke,
        strokeWidth: strokeWidth,
      selectable: false,
        evented: false,
        objectCaching: false
      });
      (this.polygonPreviewShape as any).id = 'polygon-preview';
      
      this.canvas.add(this.polygonPreviewShape);
    }
    
    this.canvas.renderAll();
  }
  
  /**
   * 清除多边形预览
   */
  private clearPolygonPreview() {
    // 移除预览线
    if (this.polygonPreviewLine) {
      this.canvas.remove(this.polygonPreviewLine);
      this.polygonPreviewLine = null;
    }
    
    // 移除预览形状
    if (this.polygonPreviewShape) {
      this.canvas.remove(this.polygonPreviewShape);
      this.polygonPreviewShape = null;
    }
    
    // 清除所有预览相关的对象
    const objectsToRemove = this.canvas.getObjects().filter((obj: any) => 
      obj.id === 'polygon-preview' || 
      obj.id === 'polygon-preview-line'
    );
    
    objectsToRemove.forEach((obj: any) => {
      this.canvas.remove(obj);
    });
  }
}

/**
 * 统一的区域/围栏尺寸标注管理器
 * 整合了围栏和区域功能，避免重复
 */
export class AreaFenceManager {
  private canvas: any;
  private fenceFactory: FenceFactory;
  private dimensionMarkers: Record<string, any[]> = {};

  constructor(canvas: any) {
    this.canvas = canvas;

    this.fenceFactory = new FenceFactory(canvas);
  }

  /**
   * 创建围栏
   */
  createFence(position: AreaPosition) {
    if (!position || position.type === 'none') return null;

    try {
      switch (position.type) {
        case 'circle':
          const circlePoints = position.points as CirclePoints;
          const circle = this.fenceFactory.addCircleFence(
            { x: circlePoints.x, y: circlePoints.y },
            circlePoints.radius
          );
          return circle;

        case 'rectangle':
          const rectPoints = position.points as RectanglePoints;
          const rectangle = this.fenceFactory.addRectangleFence(
            { x: rectPoints.x, y: rectPoints.y },
            rectPoints.width,
            rectPoints.height
          );
          return rectangle;

        case 'polygon':
          const polyPoints = position.points as PolygonPoints;
          if (polyPoints.length < 3) return null;
          
          const polygon = this.fenceFactory.createPolygonFence(polyPoints);
          return polygon;

        default:
          return null;
      }
    } catch (error) {
      console.error('创建围栏失败:', error);
      return null;
    }
  }

  /**
   * 添加区域尺寸标注
   */
  addAreaDimensionMarkers(areaData: AreaNode, isSelected: boolean = false): any[] {
    if (!areaData.id || !areaData.position) return [];

    const areaId = areaData.id;
    
    // 如果已存在，先移除旧的
    this.removeAreaDimensionMarkers(areaId);
    
    let position: AreaPosition;
    try {
      position = typeof areaData.position === 'string'
        ? JSON.parse(areaData.position)
        : areaData.position;
    } catch (e) {
      console.error('解析区域位置数据失败:', e);
      return [];
    }
    
    const markers: any[] = [];
    
    // 保存标注引用
    this.dimensionMarkers[areaId] = markers;
    return markers;
  }

  /**
   * 更新区域尺寸标注
   */
  updateAreaDimensionMarkers(areaData: AreaNode, isSelected: boolean = false, forceRecreate: boolean = false): any[] {
    if (!areaData.id) return [];
    
    const areaId = areaData.id;
    
    // 如果强制重新创建或不存在，则重新创建
    if (forceRecreate || !this.dimensionMarkers[areaId]) {
      return this.addAreaDimensionMarkers(areaData, isSelected);
    }

    // 更新现有标注
    // ...
    
    return this.dimensionMarkers[areaId] || [];
  }

  /**
   * 移除区域尺寸标注
   */
  removeAreaDimensionMarkers(areaId: number | string) {
    if (!areaId || !this.dimensionMarkers[areaId]) return;
    
    this.dimensionMarkers[areaId].forEach(marker => {
      if (marker) {
        this.canvas.remove(marker);
      }
    });
    
    delete this.dimensionMarkers[areaId];
  }

  /**
   * 移除所有区域尺寸标注
   */
  removeAllAreaDimensionMarkers() {
    Object.keys(this.dimensionMarkers).forEach(areaId => {
      this.removeAreaDimensionMarkers(areaId);
    });
    
    this.dimensionMarkers = {};
  }



  /**
   * 获取围栏工厂实例
   */
  getFenceFactory() {
    return this.fenceFactory;
  }
} 

/**
 * 实时多边形绘制工具
 * 支持鼠标跟随的实时绘制效果，类似天地图的绘制工具
 */
export class RealtimePolygonDrawer {
  private canvas: any;
  private fenceFactory: FenceFactory;
  private isDrawing: boolean = false;
  private points: { x: number; y: number }[] = [];
  private previewLines: any[] = [];
  private tempPolygon: any = null;
  private onCompleteCallback?: (points: { x: number; y: number }[]) => void;

  constructor(canvas: any, fenceFactory: FenceFactory) {
    this.canvas = canvas;
    this.fenceFactory = fenceFactory;
  }

  /**
   * 添加多边形点
   */
  addPolygonPoint(point: { x: number; y: number }) {
    if (!this.isDrawing) {
      this.startPolygonDrawing();
    }
    
    this.points.push(point);
    this.updatePolygonDisplay();
  }

  /**
   * 开始多边形绘制
   */
  startPolygonDrawing() {
    this.isDrawing = true;
    this.points = [];
    this.clearPreview();
  }

  /**
   * 更新多边形橡皮筋效果
   */
  updatePolygonRubberBand(mousePoint: { x: number; y: number }) {
    if (!this.isDrawing || this.points.length === 0) return;

    // 清除之前的预览线
    this.clearPreviewLines();

    const currentZoom = this.canvas.getZoom();
    const strokeWidth = 2 / currentZoom;

    // 如果只有一个点，绘制从第一个点到鼠标的线
    if (this.points.length === 1) {
      const previewLine = new Line(
        [this.points[0].x, this.points[0].y, mousePoint.x, mousePoint.y],
        {
          stroke: this.fenceFactory.getFenceColor().stroke,
          strokeWidth: strokeWidth,
          selectable: false,
          evented: false,
          objectCaching: false
        }
      );
      (previewLine as any).id = 'polygon-preview-line';
      this.previewLines.push(previewLine);
      this.canvas.add(previewLine);
    }
    // 如果有两个或更多点，绘制完整的预览多边形
    else if (this.points.length >= 2) {
      // 创建包含鼠标位置的临时点数组
      const previewPoints = [...this.points, mousePoint];
      
      // 创建预览多边形（使用更透明的填充）
      const fenceColor = this.fenceFactory.getFenceColor();
      const previewFill = fenceColor.color.replace(/[\d\.]+\)$/g, '0.1)'); // 将透明度改为0.1
      
      const previewPolygon = new Polygon(previewPoints, {
        fill: previewFill,
        stroke: fenceColor.stroke,
        strokeWidth: strokeWidth,
        selectable: false,
        evented: false,
        objectCaching: false
      });
      (previewPolygon as any).id = 'polygon-preview';
      
      this.previewLines.push(previewPolygon);
      this.canvas.add(previewPolygon);
    }

    this.canvas.renderAll();
  }

  /**
   * 完成多边形绘制
   */
  finishPolygonDrawing(lastPoint?: { x: number; y: number }): any {
    if (lastPoint) {
      // 检查最后一个点是否与第一个点接近，如果是则自动闭合
      const firstPoint = this.points[0];
      const distToFirst = Math.sqrt(
        Math.pow(lastPoint.x - firstPoint.x, 2) + 
        Math.pow(lastPoint.y - firstPoint.y, 2)
      );
      
      // 如果最后一个点与第一个点距离很近，就使用第一个点
      if (distToFirst > 10) {
        this.points.push(lastPoint);
      }
    }
    
    if (this.points.length < 3) {
      console.warn('多边形至少需要3个点');
      return null;
    }

    this.isDrawing = false;
    this.clearPreview();

    // 创建最终的多边形
    const finalPolygon = this.createFinalPolygon();
    
    // 保存点副本，然后清空当前点数组
    const resultPoints = [...this.points];
    this.points = [];

    console.log('多边形绘制完成，点数:', resultPoints.length);

    // 触发完成回调
    if (this.onCompleteCallback) {
      this.onCompleteCallback(resultPoints);
    }

    return finalPolygon;
  }

  /**
   * 创建最终的多边形
   */
  private createFinalPolygon() {
    const currentZoom = this.canvas.getZoom();
    const strokeWidth = 2 / currentZoom;

    const polygon = new Polygon(this.points, {
      fill: this.fenceFactory.getFenceColor().color,
      stroke: this.fenceFactory.getFenceColor().stroke,
      strokeWidth: strokeWidth,
      selectable: false,
      evented: false,
      objectCaching: false
    });
    (polygon as any).id = 'fence';

    this.canvas.add(polygon);
    this.canvas.renderAll();
    return polygon;
  }

  /**
   * 更新多边形显示
   */
  private updatePolygonDisplay() {
    // 移除旧的临时多边形
    if (this.tempPolygon) {
      this.canvas.remove(this.tempPolygon);
      this.tempPolygon = null;
    }

    // 如果有足够的点，创建临时多边形
    if (this.points.length >= 3) {
      const currentZoom = this.canvas.getZoom();
      const strokeWidth = 2 / currentZoom;

      this.tempPolygon = new Polygon(this.points, {
        fill: this.fenceFactory.getFenceColor().color,
        stroke: this.fenceFactory.getFenceColor().stroke,
        strokeWidth: strokeWidth,
        selectable: false,
        evented: false,
        objectCaching: false
      });
      (this.tempPolygon as any).id = 'temp-polygon';

      this.canvas.add(this.tempPolygon);
      this.canvas.renderAll();
    }
  }

  /**
   * 清除预览线
   */
  private clearPreviewLines() {
    this.previewLines.forEach(line => {
      if (line && this.canvas) {
        this.canvas.remove(line);
      }
    });
    this.previewLines = [];
  }

  /**
   * 清除所有预览元素
   */
  private clearPreview() {
    this.clearPreviewLines();
    
    if (this.tempPolygon) {
      this.canvas.remove(this.tempPolygon);
      this.tempPolygon = null;
    }

    // 清除所有预览相关的对象
    const objectsToRemove = this.canvas.getObjects().filter((obj: any) => 
      obj.id === 'polygon-preview' || 
      obj.id === 'polygon-preview-line' || 
      obj.id === 'temp-polygon'
    );
    
    objectsToRemove.forEach((obj: any) => {
      this.canvas.remove(obj);
    });

    this.canvas.renderAll();
  }

  /**
   * 取消多边形绘制
   */
  cancelDrawing() {
    this.isDrawing = false;
    this.points = [];
    this.clearPreview();
    console.log('取消多边形绘制');
  }

  /**
   * 设置完成回调
   */
  setOnCompleteCallback(callback: (points: { x: number; y: number }[]) => void) {
    this.onCompleteCallback = callback;
  }



  /**
   * 是否正在绘制
   */
  isCurrentlyDrawing(): boolean {
    return this.isDrawing;
  }

  /**
   * 获取当前点数
   */
  getPointCount(): number {
    return this.points.length;
  }

  /**
   * 获取当前点数组
   */
  getCurrentPoints(): { x: number; y: number }[] {
    return [...this.points];
  }

  /**
   * 直接渲染多边形（用于回显已有数据）
   */
  renderPolygon(points: { x: number; y: number }[]): any {
    if (!points || points.length < 3) {
      console.warn('多边形至少需要3个点');
      return null;
    }

    // 清除任何现有的预览
    this.clearPreview();

    const currentZoom = this.canvas.getZoom();
    // 确保线宽在高缩放级别下仍然可见
    const minStrokeWidth = 1.5 / currentZoom;
    const strokeWidth = Math.max(this.fenceFactory.getFenceColor().strokeWidth, minStrokeWidth);

    const polygon = new Polygon(points, {
      fill: this.fenceFactory.getFenceColor().color,
      stroke: this.fenceFactory.getFenceColor().stroke,
      strokeWidth: strokeWidth,
      strokeUniform: true, // 保持边框宽度一致，不随缩放变化
      noScaleCache: false,
      objectCaching: false,
      selectable: false,
      evented: false
    });
    (polygon as any).id = 'fence';

    this.canvas.add(polygon);
    this.canvas.renderAll();
    
    console.log('多边形回显完成，点数:', points.length);
    return polygon;
  }
} 