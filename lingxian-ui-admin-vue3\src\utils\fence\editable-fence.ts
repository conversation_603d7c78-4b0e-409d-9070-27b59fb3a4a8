/**
 * 可编辑围栏类
 * 支持通过点击文本编辑尺寸，围栏会实时更新
 */

import { IText, Line, Group } from 'fabric'
import FabricCanvas from '@/utils/FabricCanvas'
import { Iposition } from '@/api/master/floor/type'
import { type ScreenInfo } from '@/store/modules/canvas/drawing'
import { FenceFactory } from './fence-factory'
import { FenceUtils } from './fence-utils'
import { convertToFenceData } from './fence-core'
import type { FenceDate, FenceType, DimensionType } from './fence-types'

// 导入多边形绘制器（假设存在）
declare class RealtimePolygonDrawer {
  constructor(canvas: any, fenceFactory: FenceFactory)
  renderPolygon(points: { x: number; y: number }[]): any
}

/**
 * 为文本元素添加通用的编辑事件处理
 * @param textObj 文本对象
 * @param canvas 画布对象
 * @param onEditCallback 编辑完成回调
 */
function addCommonTextEditEvents(textObj: IText, canvas: any, onEditCallback?: (newValue: number) => void) {
  // 保存原始值以便恢复
  const originalValue = (textObj as any).originalValue || parseFloat(textObj.text)
  
  // 监听文本变化
  canvas.on('text:changed', (opt: any) => {
    if (opt.target !== textObj) return
    
    const newText = opt.target.text
    // 验证输入是否为有效数字
    if (/^[0-9]*\.?[0-9]*$/.test(newText) || newText === '') {
      // 解析新的数值
      const newValue = parseFloat(newText)
      
      // 验证数值是否有效
      if (isNaN(newValue) || newValue <= 0) {
        // 无效数值，恢复原始值
        textObj.set({ text: FenceUtils.formatValue(originalValue) })
        canvas.renderAll()
        return
      }
      
      // 应用新值（四舍五入到两位小数）
      const formattedValue = FenceUtils.formatValue(newValue)
      textObj.set({ text: formattedValue })
      
      // 调用回调函数更新围栏尺寸
      if (onEditCallback) {
        onEditCallback(newValue)
      }
    } else {
      // 恢复到上一个有效值
      setTimeout(() => {
        textObj.set({ text: '1' })
        canvas.renderAll()
      }, 10)
    }
  })
  
  // 添加双击事件进入编辑模式
  textObj.on('mousedblclick', () => {
    textObj.enterEditing()
    textObj.selectAll()
  })
}

/**
 * 可编辑围栏类
 * 支持通过点击文本编辑尺寸，围栏会实时更新
 */
export class EditableFence {
  private map: FabricCanvas
  private fenceFactory: FenceFactory
  private imgInfoSize: Iposition
  private screenInfo: ScreenInfo
  private fenceObject: any = null
  private dimensionTexts: IText[] = []
  private dimensionLines: Line[] = []
  private centerMarker: Group | null = null
  private type: FenceType
  private id: string
  private onUpdateCallback?: (fenceData: FenceDate) => void
  private originalCanvasSelection: boolean = true // 保存画布原始选择状态

  constructor(
    map: FabricCanvas,
    imgInfoSize: Iposition,
    screenInfo: ScreenInfo,
    type: FenceType,
    id: string = 'editable-fence'
  ) {
    this.map = map
    this.fenceFactory = new FenceFactory(map.canvas)
    this.imgInfoSize = imgInfoSize
    this.screenInfo = screenInfo
    this.type = type
    this.id = id
    
    // 添加缩放监听器
    this.addZoomListener()
  }

  /**
   * 设置更新回调函数
   */
  setUpdateCallback(callback: (fenceData: FenceDate) => void): void {
    this.onUpdateCallback = callback
  }

  /**
   * 添加缩放监听器
   */
  private addZoomListener(): void {
    this.map.canvas.on('after:render', () => {
      this.updateElementsForZoom()
    })
  }

  /**
   * 创建圆形围栏
   */
  createCircleFence(center: { x: number; y: number }, radius: number): EditableFence {
    this.type = 'circle'
    
    // 创建圆形围栏
    this.fenceObject = this.fenceFactory.addCircleFence(center, radius)
    
    // 应用公共样式和圆形特定样式
    FenceUtils.applyCommonFenceStyle(this.fenceObject, this.id, true)
    this.fenceObject.set({
      lockRotation: true, // 锁定旋转
      lockUniScaling: true, // 锁定不均匀缩放，确保圆形保持圆形
      lockScalingX: false, // 允许X方向缩放
      lockScalingY: false, // 允许Y方向缩放
      lockScalingFlip: true, // 防止翻转
      originX: 'center', // 设置原点为中心
      originY: 'center', // 设置原点为中心
      centeredScaling: true, // 从中心点缩放
      centeredRotation: true, // 从中心点旋转
    })

    // 添加半径线和文本
    this.addRadiusDimension(center, radius)
    
    // 添加圆形调整事件监听
    this.addCircleResizeEvents()
    
    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建矩形围栏
   */
  createRectangleFence(position: { x: number; y: number }, width: number, height: number): EditableFence {
    this.type = 'rectangle'
    
    // 创建矩形围栏
    this.fenceObject = this.fenceFactory.addRectangleFence(position, width, height)
    
    // 应用公共样式和矩形特定样式
    FenceUtils.applyCommonFenceStyle(this.fenceObject, this.id, true)
    this.fenceObject.set({
      lockRotation: true, // 锁定旋转
      lockUniScaling: false, // 允许不均匀缩放，可以单独调整宽高
    })

    // 添加尺寸标注
    this.addRectangleDimensions(position, width, height)
    
    // 添加矩形调整事件监听
    this.addRectangleResizeEvents()
    
    this.map.canvas.renderAll()
    return this
  }

  /**
   * 创建多边形围栏
   */
  createPolygonFence(points: { x: number; y: number }[]): EditableFence {
    this.type = 'polygon'
    
    // 使用RealtimePolygonDrawer创建多边形围栏
    const polygonDrawer = new RealtimePolygonDrawer(this.map.canvas, this.fenceFactory)
    this.fenceObject = polygonDrawer.renderPolygon(points)
    
    if (this.fenceObject) {
      // 应用公共样式和多边形特定样式
      FenceUtils.applyCommonFenceStyle(this.fenceObject, this.id, true)
      this.fenceObject.set({
        lockRotation: false, // 多边形允许旋转
        noScaleCache: false,
        objectCaching: false
      })
    }

    this.map.canvas.renderAll()
    return this
  }

  /**
   * 获取围栏数据
   */
  getFenceData(): FenceDate {
    if (!this.fenceObject) {
      return { type: this.type, points: null }
    }
    
    return convertToFenceData(this.type, this.fenceObject, this.map, this.imgInfoSize, this.screenInfo)
  }

  /**
   * 销毁围栏及其所有相关元素
   */
  destroy(): void {
    // 移除围栏对象
    if (this.fenceObject) {
      this.map.canvas.remove(this.fenceObject)
    }
    
    // 移除尺寸文本
    this.dimensionTexts.forEach(text => {
      this.map.canvas.remove(text)
    })
    
    // 移除尺寸线
    this.dimensionLines.forEach(line => {
      this.map.canvas.remove(line)
    })
    
    // 移除圆心标记
    if (this.centerMarker) {
      this.map.canvas.remove(this.centerMarker)
    }
    
    // 恢复画布拖动功能
    this.restoreCanvasDragging()
    
    // 清空数组
    this.dimensionTexts = []
    this.dimensionLines = []
    this.centerMarker = null
    this.fenceObject = null
    
    this.map.canvas.renderAll()
  }

  /**
   * 设置围栏样式
   */
  setStyle(style: { fill?: string; stroke?: string; strokeWidth?: number }): void {
    if (this.fenceObject) {
      this.fenceObject.set(style)
      this.map.canvas.renderAll()
    }
  }

  /**
   * 获取围栏对象
   */
  getFenceObject(): any {
    return this.fenceObject
  }

  /**
   * 获取围栏工厂实例
   */
  getFenceFactory(): FenceFactory {
    return this.fenceFactory
  }

  // 私有方法声明（实现将在后续添加）
  private updateElementsForZoom(): void {
    // 实现将在后续添加
  }

  private addRadiusDimension(center: { x: number; y: number }, radius: number): void {
    // 实现将在后续添加
  }

  private addRectangleDimensions(position: { x: number; y: number }, width: number, height: number): void {
    // 实现将在后续添加
  }

  private addCircleResizeEvents(): void {
    // 实现将在后续添加
  }

  private addRectangleResizeEvents(): void {
    // 实现将在后续添加
  }

  private restoreCanvasDragging(): void {
    // 实现将在后续添加
  }
}
